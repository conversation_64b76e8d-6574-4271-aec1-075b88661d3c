import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { subscriptionPlans, userSubscriptions } from "@/lib/db/schema";
import { eq, and, or, desc, asc } from "drizzle-orm";
import { z } from "zod";

const updatePlanSchema = z.object({
  name: z.string().min(2).max(100).optional(),
  displayName: z.string().min(2).max(100).optional(),
  description: z.string().optional(),
  price: z.string().regex(/^\d+(\.\d{1,2})?$/, "Invalid price format").optional(),
  currency: z.string().length(3).optional(),
  billingCycle: z.enum(['monthly', 'yearly']).optional(),
  customBillingMonths: z.number().int().min(1).max(120).optional().nullable(),
  customBillingYears: z.number().int().min(1).max(10).optional().nullable(),
  features: z.array(z.string()).optional(),
  maxPosts: z.number().int().optional(),
  maxStorage: z.number().int().optional(),
  maxGroups: z.number().int().optional(),
  canCreateFanPages: z.boolean().optional(),
  canCreateStores: z.boolean().optional(),
  canMonetizeBlogs: z.boolean().optional(),
  prioritySupport: z.boolean().optional(),
  grantsVerification: z.boolean().optional(),
  // Badge system fields
  badgeType: z.enum(['none', 'crown', 'star', 'diamond', 'vip', 'custom']).optional(),
  badgeColor: z.string().regex(/^#[0-9A-F]{6}$/i, "Invalid hex color").optional(),
  customBadgeUrl: z.string().url().optional().or(z.literal('')),
  badgePriority: z.number().int().min(0).max(100).optional(),
  isActive: z.boolean().optional(),
  sortOrder: z.number().int().optional(),
});

// GET - Fetch single subscription plan
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized - Admin access required" },
        { status: 401 }
      );
    }

    const plan = await db.query.subscriptionPlans.findFirst({
      where: eq(subscriptionPlans.id, params.id),
    });

    if (!plan) {
      return NextResponse.json(
        { message: "Subscription plan not found" },
        { status: 404 }
      );
    }

    // Get subscription count for this plan
    const subscriptionCount = await db
      .select({ count: userSubscriptions.id })
      .from(userSubscriptions)
      .where(
        and(
          eq(userSubscriptions.planId, params.id),
          or(
            eq(userSubscriptions.status, 'active'),
            eq(userSubscriptions.status, 'pending')
          )
        )
      );

    return NextResponse.json({
      success: true,
      plan: {
        ...plan,
        activeSubscriptions: subscriptionCount.length,
      },
    });
  } catch (error) {
    console.error("Error fetching subscription plan:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to fetch subscription plan",
      },
      { status: 500 }
    );
  }
}

// PATCH - Update subscription plan
export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized - Admin access required" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = updatePlanSchema.parse(body);

    // Check if plan exists
    const existingPlan = await db.query.subscriptionPlans.findFirst({
      where: eq(subscriptionPlans.id, params.id),
    });

    if (!existingPlan) {
      return NextResponse.json(
        { message: "Subscription plan not found" },
        { status: 404 }
      );
    }

    // If updating name, check for duplicates
    if (validatedData.name && validatedData.name !== existingPlan.name) {
      const duplicatePlan = await db.query.subscriptionPlans.findFirst({
        where: and(
          eq(subscriptionPlans.name, validatedData.name),
          // Make sure it's not the same plan we're updating
          // Use NOT EQUAL instead of EQUAL
        ),
      });

      if (duplicatePlan && duplicatePlan.id !== params.id) {
        return NextResponse.json(
          {
            success: false,
            message: "A plan with this name already exists",
          },
          { status: 400 }
        );
      }
    }

    // Update the plan
    await db
      .update(subscriptionPlans)
      .set({
        ...validatedData,
        updatedAt: new Date(),
      })
      .where(eq(subscriptionPlans.id, params.id));

    // Fetch updated plan
    const updatedPlan = await db.query.subscriptionPlans.findFirst({
      where: eq(subscriptionPlans.id, params.id),
    });

    return NextResponse.json({
      success: true,
      message: "Subscription plan updated successfully",
      plan: updatedPlan,
    });
  } catch (error) {
    console.error("Error updating subscription plan:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          message: "Validation error",
          errors: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        message: "Failed to update subscription plan",
      },
      { status: 500 }
    );
  }
}

// DELETE - Delete subscription plan
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized - Admin access required" },
        { status: 401 }
      );
    }

    // Check if plan exists
    const existingPlan = await db.query.subscriptionPlans.findFirst({
      where: eq(subscriptionPlans.id, params.id),
    });

    if (!existingPlan) {
      return NextResponse.json(
        { message: "Subscription plan not found" },
        { status: 404 }
      );
    }

    // Check if plan has active subscriptions
    const activeSubscriptions = await db.query.userSubscriptions.findMany({
      where: and(
        eq(userSubscriptions.planId, params.id),
        or(
          eq(userSubscriptions.status, 'active'),
          eq(userSubscriptions.status, 'pending')
        )
      ),
    });

    if (activeSubscriptions.length > 0) {
      return NextResponse.json(
        {
          success: false,
          message: `Cannot delete plan with ${activeSubscriptions.length} active subscription(s). Please cancel or migrate these subscriptions first.`,
        },
        { status: 400 }
      );
    }

    // Delete the plan
    await db.delete(subscriptionPlans).where(eq(subscriptionPlans.id, params.id));

    return NextResponse.json({
      success: true,
      message: "Subscription plan deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting subscription plan:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to delete subscription plan",
      },
      { status: 500 }
    );
  }
}
