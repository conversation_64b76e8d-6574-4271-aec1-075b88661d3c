"use client";

import { useState, useEffect } from "react";
import { CheckIcon, SparklesIcon } from "@heroicons/react/24/outline";
import { Button } from "@/components/ui/Button";
import { toast } from "react-hot-toast";
import { getBillingCycleDisplay } from "@/lib/utils/billingUtils";

interface SubscriptionPlan {
  id: string;
  name: string;
  displayName: string;
  description: string;
  price: string;
  currency: string;
  billingCycle: string;
  customBillingMonths: number | null;
  customBillingYears: number | null;
  features: string[];
  maxPosts: number;
  maxStorage: number;
  maxGroups: number;
  canCreateFanPages: boolean;
  canCreateStores: boolean;
  canMonetizeBlogs: boolean;
  prioritySupport: boolean;
  isActive: boolean;
  sortOrder: number;
}

interface PlanComparisonProps {
  currentPlanId?: string;
  onSelectPlan: (planId: string) => void;
  showCurrentPlan?: boolean;
  loading?: boolean;
}

export function PlanComparison({ currentPlanId, onSelectPlan, showCurrentPlan = true, loading: externalLoading = false }: PlanComparisonProps) {
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchPlans();
  }, []);

  const fetchPlans = async () => {
    try {
      const response = await fetch('/api/subscription-plans');
      const result = await response.json();

      if (result.success) {
        setPlans(result.data);
      } else {
        toast.error('Failed to fetch subscription plans');
      }
    } catch (error) {
      console.error('Error fetching plans:', error);
      toast.error('Failed to fetch subscription plans');
    } finally {
      setLoading(false);
    }
  };

  const formatStorageLimit = (storage: number) => {
    if (storage === -1) return 'Unlimited';
    if (storage >= 1024) return `${(storage / 1024).toFixed(0)}GB`;
    return `${storage}MB`;
  };

  const formatPostLimit = (posts: number) => {
    if (posts === -1) return 'Unlimited';
    return `${posts} posts/month`;
  };

  const formatGroupLimit = (groups: number) => {
    if (groups === -1) return 'Unlimited';
    return `${groups} groups`;
  };

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        {/* Header Skeleton */}
        <div className="text-center space-y-3">
          <div className="h-8 bg-gray-200 rounded w-1/3 mx-auto animate-pulse"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2 mx-auto animate-pulse"></div>
        </div>

        {/* Plans Grid Skeleton */}
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="animate-pulse bg-white rounded-2xl border border-gray-200 p-6 space-y-4">
              <div className="flex justify-between items-center">
                <div className="h-6 bg-gray-200 rounded w-1/2"></div>
                <div className="h-5 bg-gray-200 rounded w-1/4"></div>
              </div>
              <div className="h-8 bg-gray-200 rounded w-1/3"></div>
              <div className="h-4 bg-gray-200 rounded w-full"></div>
              <div className="space-y-2">
                {[1, 2, 3, 4].map((j) => (
                  <div key={j} className="flex items-center space-x-2">
                    <div className="h-4 w-4 bg-gray-200 rounded"></div>
                    <div className="h-4 bg-gray-200 rounded flex-1"></div>
                  </div>
                ))}
              </div>
              <div className="h-10 bg-gray-200 rounded"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="inline-flex items-center justify-center p-3 bg-blue-100 rounded-full">
          <SparklesIcon className="h-8 w-8 text-blue-600" />
        </div>
        <div>
          <h2 className="text-3xl font-bold text-gray-900">Choose Your Perfect Plan</h2>
          <p className="mt-3 text-lg text-gray-600 max-w-2xl mx-auto">
            Unlock powerful features and take your experience to the next level.
            Upgrade or downgrade anytime with no commitment.
          </p>
        </div>
      </div>

      {/* Plans Grid */}
      <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4">
        {plans.map((plan) => {
          const isCurrentPlan = currentPlanId === plan.id;
          const isFree = parseFloat(plan.price) === 0;
          const isPopular = plan.name === 'premium';

          return (
            <div
              key={plan.id}
              className={`relative overflow-hidden rounded-2xl bg-white shadow-lg border-2 transition-all duration-300 hover:shadow-xl hover:-translate-y-1 ${
                isCurrentPlan
                  ? 'border-blue-500 ring-2 ring-blue-200'
                  : isPopular
                  ? 'border-purple-500 ring-2 ring-purple-200'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              {isPopular && (
                <div className="absolute top-0 right-0 bg-gradient-to-r from-purple-500 to-purple-600 text-white px-4 py-2 text-xs font-semibold rounded-bl-2xl shadow-lg">
                  ⭐ Most Popular
                </div>
              )}

              {isCurrentPlan && (
                <div className="absolute top-0 left-0 bg-gradient-to-r from-blue-500 to-blue-600 text-white px-4 py-2 text-xs font-semibold rounded-br-2xl shadow-lg">
                  ✓ Current Plan
                </div>
              )}

              <div className="p-8">
                <div className="text-center mb-6">
                  <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full mb-4 ${
                    isPopular ? 'bg-purple-100' : isFree ? 'bg-green-100' : 'bg-blue-100'
                  }`}>
                    <SparklesIcon className={`h-8 w-8 ${
                      isPopular ? 'text-purple-600' : isFree ? 'text-green-600' : 'text-blue-600'
                    }`} />
                  </div>

                  <h3 className="text-xl font-bold text-gray-900 mb-2">
                    {plan.displayName}
                  </h3>

                  <div className="mb-4">
                    {isFree ? (
                      <div className="text-4xl font-bold text-green-600">Free</div>
                    ) : (
                      <div className="flex items-baseline justify-center">
                        <span className="text-4xl font-bold text-gray-900">${plan.price}</span>
                        <span className="text-lg text-gray-500 ml-1">
                          /{getBillingCycleDisplay(
                            plan.billingCycle as 'monthly' | 'yearly',
                            plan.customBillingMonths,
                            plan.customBillingYears
                          ).displayText.toLowerCase()}
                        </span>
                      </div>
                    )}
                  </div>

                  <p className="text-sm text-gray-600 leading-relaxed">
                    {plan.description}
                  </p>
                </div>

                {/* Key Features */}
                <div className="space-y-4 mb-8">
                  <div className="bg-gray-50 rounded-xl p-4">
                    <h4 className="text-sm font-semibold text-gray-900 mb-3">Core Features</h4>
                    <ul className="space-y-3">
                      <li className="flex items-center">
                        <div className="flex-shrink-0 w-5 h-5 bg-green-100 rounded-full flex items-center justify-center">
                          <CheckIcon className="h-3 w-3 text-green-600" />
                        </div>
                        <span className="text-sm text-gray-700 ml-3 font-medium">
                          {formatPostLimit(plan.maxPosts)}
                        </span>
                      </li>
                      <li className="flex items-center">
                        <div className="flex-shrink-0 w-5 h-5 bg-green-100 rounded-full flex items-center justify-center">
                          <CheckIcon className="h-3 w-3 text-green-600" />
                        </div>
                        <span className="text-sm text-gray-700 ml-3 font-medium">
                          {formatStorageLimit(plan.maxStorage)} storage
                        </span>
                      </li>
                      <li className="flex items-center">
                        <div className="flex-shrink-0 w-5 h-5 bg-green-100 rounded-full flex items-center justify-center">
                          <CheckIcon className="h-3 w-3 text-green-600" />
                        </div>
                        <span className="text-sm text-gray-700 ml-3 font-medium">
                          {formatGroupLimit(plan.maxGroups)}
                        </span>
                      </li>
                    </ul>
                  </div>

                  {/* Premium Features */}
                  {(plan.canCreateFanPages || plan.canCreateStores || plan.canMonetizeBlogs || plan.prioritySupport) && (
                    <div className="bg-blue-50 rounded-xl p-4">
                      <h4 className="text-sm font-semibold text-blue-900 mb-3">Premium Features</h4>
                      <ul className="space-y-3">
                        {plan.canCreateFanPages && (
                          <li className="flex items-center">
                            <div className="flex-shrink-0 w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center">
                              <CheckIcon className="h-3 w-3 text-blue-600" />
                            </div>
                            <span className="text-sm text-blue-800 ml-3 font-medium">Create fan pages</span>
                          </li>
                        )}
                        {plan.canCreateStores && (
                          <li className="flex items-center">
                            <div className="flex-shrink-0 w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center">
                              <CheckIcon className="h-3 w-3 text-blue-600" />
                            </div>
                            <span className="text-sm text-blue-800 ml-3 font-medium">Create stores</span>
                          </li>
                        )}
                        {plan.canMonetizeBlogs && (
                          <li className="flex items-center">
                            <div className="flex-shrink-0 w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center">
                              <CheckIcon className="h-3 w-3 text-blue-600" />
                            </div>
                            <span className="text-sm text-blue-800 ml-3 font-medium">Blog monetization</span>
                          </li>
                        )}
                        {plan.prioritySupport && (
                          <li className="flex items-center">
                            <div className="flex-shrink-0 w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center">
                              <CheckIcon className="h-3 w-3 text-blue-600" />
                            </div>
                            <span className="text-sm text-blue-800 ml-3 font-medium">Priority support</span>
                          </li>
                        )}
                      </ul>
                    </div>
                  )}

                  {/* Additional Features */}
                  {plan.features && plan.features.length > 0 && (
                    <div className="bg-purple-50 rounded-xl p-4">
                      <h4 className="text-sm font-semibold text-purple-900 mb-3">
                        Bonus Features
                      </h4>
                      <ul className="space-y-2">
                        {plan.features.slice(0, 2).map((feature, index) => (
                          <li key={index} className="flex items-center">
                            <div className="flex-shrink-0 w-4 h-4 bg-purple-100 rounded-full flex items-center justify-center">
                              <div className="w-2 h-2 bg-purple-600 rounded-full"></div>
                            </div>
                            <span className="text-sm text-purple-800 ml-3">{feature}</span>
                          </li>
                        ))}
                        {plan.features.length > 2 && (
                          <li className="text-xs text-purple-600 ml-7 font-medium">
                            +{plan.features.length - 2} more features
                          </li>
                        )}
                      </ul>
                    </div>
                  )}
                </div>

                <Button
                  onClick={() => onSelectPlan(plan.id)}
                  disabled={isCurrentPlan || externalLoading}
                  className={`w-full py-4 px-6 rounded-xl font-semibold text-sm transition-all duration-200 ${
                    isCurrentPlan || externalLoading
                      ? 'bg-gray-100 text-gray-500 cursor-not-allowed border border-gray-200'
                      : isPopular
                      ? 'bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white shadow-lg hover:shadow-xl'
                      : isFree
                      ? 'bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white shadow-lg hover:shadow-xl'
                      : 'bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg hover:shadow-xl'
                  }`}
                >
                  {externalLoading ? (
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                      Processing...
                    </div>
                  ) : isCurrentPlan ? (
                    <div className="flex items-center justify-center">
                      <CheckIcon className="h-5 w-5 mr-2" />
                      Current Plan
                    </div>
                  ) : isFree ? (
                    <div className="flex items-center justify-center">
                      <SparklesIcon className="h-5 w-5 mr-2" />
                      Get Started Free
                    </div>
                  ) : (
                    <div className="flex items-center justify-center">
                      <SparklesIcon className="h-5 w-5 mr-2" />
                      {currentPlanId === 'free-plan' ? 'Upgrade Now' : 'Select Plan'}
                    </div>
                  )}
                </Button>
              </div>
            </div>
          );
        })}
      </div>

      {/* Feature Comparison Table */}
      <div className="mt-12">
        <h3 className="text-lg font-medium text-gray-900 mb-6">
          Detailed Feature Comparison
        </h3>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Feature
                </th>
                {plans.map((plan) => (
                  <th key={plan.id} className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {plan.displayName}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              <tr>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  Posts per month
                </td>
                {plans.map((plan) => (
                  <td key={plan.id} className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                    {formatPostLimit(plan.maxPosts)}
                  </td>
                ))}
              </tr>
              <tr>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  Storage
                </td>
                {plans.map((plan) => (
                  <td key={plan.id} className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                    {formatStorageLimit(plan.maxStorage)}
                  </td>
                ))}
              </tr>
              <tr>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  Groups
                </td>
                {plans.map((plan) => (
                  <td key={plan.id} className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                    {formatGroupLimit(plan.maxGroups)}
                  </td>
                ))}
              </tr>
              <tr>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  Fan Pages
                </td>
                {plans.map((plan) => (
                  <td key={plan.id} className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                    {plan.canCreateFanPages ? (
                      <CheckIcon className="h-5 w-5 text-green-500 mx-auto" />
                    ) : (
                      <span className="text-gray-300">—</span>
                    )}
                  </td>
                ))}
              </tr>
              <tr>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  Marketplace Stores
                </td>
                {plans.map((plan) => (
                  <td key={plan.id} className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                    {plan.canCreateStores ? (
                      <CheckIcon className="h-5 w-5 text-green-500 mx-auto" />
                    ) : (
                      <span className="text-gray-300">—</span>
                    )}
                  </td>
                ))}
              </tr>
              <tr>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  Blog Monetization
                </td>
                {plans.map((plan) => (
                  <td key={plan.id} className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                    {plan.canMonetizeBlogs ? (
                      <CheckIcon className="h-5 w-5 text-green-500 mx-auto" />
                    ) : (
                      <span className="text-gray-300">—</span>
                    )}
                  </td>
                ))}
              </tr>
              <tr>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  Priority Support
                </td>
                {plans.map((plan) => (
                  <td key={plan.id} className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                    {plan.prioritySupport ? (
                      <CheckIcon className="h-5 w-5 text-green-500 mx-auto" />
                    ) : (
                      <span className="text-gray-300">—</span>
                    )}
                  </td>
                ))}
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
