/**
 * Utility functions for handling billing cycle display and calculations
 */

export interface BillingCycleInfo {
  displayText: string;
  periodInMonths: number;
  isCustom: boolean;
}

/**
 * Get display text for billing cycle based on plan data
 * @param billingCycle - Standard billing cycle ('monthly' | 'yearly')
 * @param customBillingMonths - Custom months (nullable)
 * @param customBillingYears - Custom years (nullable)
 * @returns Formatted display text for the billing cycle
 */
export function getBillingCycleDisplay(
  billingCycle: 'monthly' | 'yearly',
  customBillingMonths?: number | null,
  customBillingYears?: number | null
): BillingCycleInfo {
  // Check if custom billing is set and not zero
  if (customBillingMonths && customBillingMonths > 0) {
    const monthText = customBillingMonths === 1 ? 'month' : 'months';
    return {
      displayText: `Every ${customBillingMonths} ${monthText}`,
      periodInMonths: customBillingMonths,
      isCustom: true
    };
  }

  if (customBillingYears && customBillingYears > 0) {
    const yearText = customBillingYears === 1 ? 'year' : 'years';
    return {
      displayText: `Every ${customBillingYears} ${yearText}`,
      periodInMonths: customBillingYears * 12,
      isCustom: true
    };
  }

  // Fall back to standard billing cycle
  switch (billingCycle) {
    case 'monthly':
      return {
        displayText: 'Monthly',
        periodInMonths: 1,
        isCustom: false
      };
    case 'yearly':
      return {
        displayText: 'Yearly',
        periodInMonths: 12,
        isCustom: false
      };
    default:
      return {
        displayText: 'Monthly',
        periodInMonths: 1,
        isCustom: false
      };
  }
}

/**
 * Check if custom billing fields should be hidden (empty or zero values)
 * @param customBillingMonths - Custom months value
 * @param customBillingYears - Custom years value
 * @returns true if both fields are empty/zero and should be hidden
 */
export function shouldHideCustomBilling(
  customBillingMonths?: number | null,
  customBillingYears?: number | null
): boolean {
  const monthsEmpty = !customBillingMonths || customBillingMonths === 0;
  const yearsEmpty = !customBillingYears || customBillingYears === 0;
  return monthsEmpty && yearsEmpty;
}

/**
 * Get Bengali display text for billing cycle
 * @param billingCycle - Standard billing cycle
 * @param customBillingMonths - Custom months
 * @param customBillingYears - Custom years
 * @returns Bengali formatted display text
 */
export function getBillingCycleDisplayBengali(
  billingCycle: 'monthly' | 'yearly',
  customBillingMonths?: number | null,
  customBillingYears?: number | null
): string {
  if (customBillingMonths && customBillingMonths > 0) {
    return `প্রতি ${customBillingMonths} মাস`;
  }

  if (customBillingYears && customBillingYears > 0) {
    return `প্রতি ${customBillingYears} বছর`;
  }

  switch (billingCycle) {
    case 'monthly':
      return 'মাসিক';
    case 'yearly':
      return 'বার্ষিক';
    default:
      return 'মাসিক';
  }
}

/**
 * Calculate end date based on billing cycle and custom periods
 * @param startDate - Start date
 * @param billingCycle - Standard billing cycle
 * @param customBillingMonths - Custom months
 * @param customBillingYears - Custom years
 * @returns End date
 */
export function calculateEndDate(
  startDate: Date,
  billingCycle: 'monthly' | 'yearly',
  customBillingMonths?: number | null,
  customBillingYears?: number | null
): Date {
  const endDate = new Date(startDate);

  if (customBillingMonths && customBillingMonths > 0) {
    endDate.setMonth(endDate.getMonth() + customBillingMonths);
    return endDate;
  }

  if (customBillingYears && customBillingYears > 0) {
    endDate.setFullYear(endDate.getFullYear() + customBillingYears);
    return endDate;
  }

  // Standard billing cycle
  if (billingCycle === 'yearly') {
    endDate.setFullYear(endDate.getFullYear() + 1);
  } else {
    endDate.setMonth(endDate.getMonth() + 1);
  }

  return endDate;
}
