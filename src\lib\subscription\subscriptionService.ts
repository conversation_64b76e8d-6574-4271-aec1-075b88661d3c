import { db } from "@/lib/db";
import {
  subscriptionPlans,
  userSubscriptions,
  subscriptionTransactions,
  users,
  referrals,
} from "@/lib/db/schema";
import { eq, and, desc, gte, lte, or } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";
import { CommissionService } from "@/lib/referral/commissionService";
import { calculateEndDate } from "@/lib/utils/billingUtils";

export interface SubscriptionPlan {
  id: string;
  name: string;
  displayName: string;
  description: string | null;
  price: string;
  currency: string;
  billingCycle: 'monthly' | 'yearly';
  customBillingMonths: number | null;
  customBillingYears: number | null;
  features: string[];
  maxPosts: number;
  maxStorage: number;
  maxGroups: number;
  canCreateFanPages: boolean;
  canCreateStores: boolean;
  canMonetizeBlogs: boolean;
  prioritySupport: boolean;
  grantsVerification: boolean;
  // Badge system fields
  badgeType: 'none' | 'crown' | 'star' | 'diamond' | 'vip' | 'custom';
  badgeColor: string;
  customBadgeUrl: string | null;
  badgePriority: number;
  isActive: boolean;
  sortOrder: number;
}

export interface UserSubscription {
  id: string;
  userId: string;
  planId: string;
  status: 'active' | 'cancelled' | 'expired' | 'pending' | 'suspended';
  startDate: Date;
  endDate: Date;
  nextBillingDate: Date | null;
  cancelledAt: Date | null;
  cancelReason: string | null;
  autoRenew: boolean;
  paymentMethod: string | null;
  lastPaymentDate: Date | null;
  plan?: SubscriptionPlan;
}

export interface SubscriptionTransaction {
  id: string;
  subscriptionId: string;
  userId: string;
  planId: string;
  type: 'payment' | 'refund' | 'upgrade' | 'downgrade' | 'cancellation';
  amount: string;
  currency: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  paymentGateway: string | null;
  gatewayTransactionId: string | null;
  description: string | null;
  processedAt: Date | null;
  createdAt: Date;
}

export interface CreateSubscriptionData {
  userId: string;
  planId: string;
  paymentMethod?: string;
  startDate?: Date;
  billingCycle?: 'monthly' | 'yearly';
}

export interface UpgradeSubscriptionData {
  subscriptionId: string;
  newPlanId: string;
  paymentMethod?: string;
}

export class SubscriptionService {
  // Get all active subscription plans
  static async getSubscriptionPlans(): Promise<SubscriptionPlan[]> {
    const plans = await db.query.subscriptionPlans.findMany({
      where: eq(subscriptionPlans.isActive, true),
      orderBy: [subscriptionPlans.sortOrder],
    });

    return plans.map(plan => ({
      ...plan,
      features: plan.features || [],
    }));
  }

  // Get a specific subscription plan
  static async getSubscriptionPlan(planId: string): Promise<SubscriptionPlan | null> {
    const plan = await db.query.subscriptionPlans.findFirst({
      where: and(
        eq(subscriptionPlans.id, planId),
        eq(subscriptionPlans.isActive, true)
      ),
    });

    if (!plan) return null;

    return {
      ...plan,
      features: plan.features || [],
    };
  }

  // Get user's current subscription
  static async getUserSubscription(userId: string): Promise<UserSubscription | null> {
    const subscription = await db.query.userSubscriptions.findFirst({
      where: and(
        eq(userSubscriptions.userId, userId),
        or(
          eq(userSubscriptions.status, 'active'),
          eq(userSubscriptions.status, 'pending')
        )
      ),
      with: {
        plan: true,
      },
      orderBy: [desc(userSubscriptions.createdAt)],
    });

    if (!subscription) return null;

    return {
      ...subscription,
      plan: subscription.plan ? {
        ...subscription.plan,
        features: subscription.plan.features || [],
      } : undefined,
    };
  }

  // Check if user has active subscription
  static async hasActiveSubscription(userId: string): Promise<boolean> {
    const subscription = await this.getUserSubscription(userId);
    return subscription?.status === 'active' && new Date() < subscription.endDate;
  }

  // Get user's subscription status and plan details
  static async getUserSubscriptionStatus(userId: string) {
    const subscription = await this.getUserSubscription(userId);
    
    if (!subscription) {
      // User has no subscription, assign free plan
      const freePlan = await this.getSubscriptionPlan('free-plan');
      return {
        hasSubscription: false,
        isActive: true, // Free plan is always active
        plan: freePlan,
        subscription: null,
        daysRemaining: null,
        nextBillingDate: null,
      };
    }

    const now = new Date();
    const isActive = subscription.status === 'active' && subscription.endDate > now;
    const daysRemaining = isActive 
      ? Math.ceil((subscription.endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
      : 0;

    return {
      hasSubscription: true,
      isActive,
      plan: subscription.plan,
      subscription,
      daysRemaining,
      nextBillingDate: subscription.nextBillingDate,
    };
  }

  // Create a new subscription
  static async createSubscription(data: CreateSubscriptionData): Promise<string> {
    try {
      const subscriptionId = uuidv4();
      const startDate = data.startDate || new Date();

      // Validate user exists
      if (!data.userId) {
        throw new Error('User ID is required');
      }

      // Calculate end date based on billing cycle
      const plan = await this.getSubscriptionPlan(data.planId);
      if (!plan) {
        throw new Error(`Subscription plan not found: ${data.planId}`);
      }

      if (!plan.isActive) {
        throw new Error('Selected subscription plan is not available');
      }

    // Calculate end date using custom billing periods if available
    const endDate = calculateEndDate(
      startDate,
      plan.billingCycle,
      plan.customBillingMonths,
      plan.customBillingYears
    );

    const nextBillingDate = new Date(endDate);

    // Cancel any existing active subscriptions
    await db
      .update(userSubscriptions)
      .set({
        status: 'cancelled',
        cancelledAt: new Date(),
        cancelReason: 'Upgraded to new plan',
        autoRenew: false,
      })
      .where(and(
        eq(userSubscriptions.userId, data.userId),
        or(
          eq(userSubscriptions.status, 'active'),
          eq(userSubscriptions.status, 'pending')
        )
      ));

      // Create new subscription
      await db.insert(userSubscriptions).values({
        id: subscriptionId,
        userId: data.userId,
        planId: data.planId,
        status: 'pending', // Will be activated after payment
        startDate,
        endDate,
        nextBillingDate,
        autoRenew: true,
        paymentMethod: data.paymentMethod || null,
      });

      return subscriptionId;
    } catch (error) {
      console.error('Error creating subscription:', error);
      throw new Error(`Failed to create subscription: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Activate a subscription (after successful payment)
  static async activateSubscription(subscriptionId: string, transactionId?: string): Promise<void> {
    try {
      if (!subscriptionId) {
        throw new Error('Subscription ID is required');
      }

      const result = await db
        .update(userSubscriptions)
        .set({
          status: 'active',
          lastPaymentDate: new Date(),
        })
        .where(eq(userSubscriptions.id, subscriptionId));

      // Verify the subscription was updated
      const updatedSubscription = await db.query.userSubscriptions.findFirst({
        where: eq(userSubscriptions.id, subscriptionId),
      });

      if (!updatedSubscription) {
        throw new Error('Subscription not found');
      }

      if (updatedSubscription.status !== 'active') {
        throw new Error('Failed to activate subscription');
      }

      // Process referral commission if applicable
      if (transactionId) {
        await this.processReferralCommission(subscriptionId, transactionId, updatedSubscription);
      }

    } catch (error) {
      console.error('Error activating subscription:', error);
      throw new Error(`Failed to activate subscription: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Process referral commission for subscription
  private static async processReferralCommission(
    subscriptionId: string,
    transactionId: string,
    subscription: any
  ): Promise<void> {
    try {
      // Get the subscription transaction
      const transaction = await db.query.subscriptionTransactions.findFirst({
        where: eq(subscriptionTransactions.id, transactionId),
      });

      if (!transaction || transaction.commissionProcessed) {
        return; // Already processed or transaction not found
      }

      // Get the subscription plan
      const plan = await this.getSubscriptionPlan(subscription.planId);
      if (!plan) {
        console.error('Subscription plan not found for commission processing');
        return;
      }

      // Check if user was referred
      const referral = await db.query.referrals.findFirst({
        where: eq(referrals.referredUserId, subscription.userId),
      });

      if (!referral) {
        // Mark transaction as commission processed (no referral)
        await db
          .update(subscriptionTransactions)
          .set({
            commissionProcessed: true,
            isFirstPurchase: await this.isFirstPurchase(subscription.userId),
            referralId: null,
            referrerId: null,
            updatedAt: new Date(),
          })
          .where(eq(subscriptionTransactions.id, transactionId));
        return;
      }

      // Update transaction with referral information
      const isFirstPurchase = await this.isFirstPurchase(subscription.userId);
      await db
        .update(subscriptionTransactions)
        .set({
          referralId: referral.id,
          referrerId: referral.referrerId,
          isFirstPurchase,
          updatedAt: new Date(),
        })
        .where(eq(subscriptionTransactions.id, transactionId));

      // Process the commission
      const commissionResult = await CommissionService.processCommission(
        transactionId,
        parseFloat(transaction.amount),
        subscription.planId,
        plan.displayName,
        subscription.userId,
        subscriptionId
      );

      if (commissionResult.success) {
        console.log(`Commission processed successfully: ${commissionResult.message}`);
      } else {
        console.log(`Commission processing failed: ${commissionResult.message}`);
      }

    } catch (error) {
      console.error('Error processing referral commission:', error);
      // Don't throw error to avoid breaking subscription activation
    }
  }

  // Check if this is user's first subscription purchase
  private static async isFirstPurchase(userId: string): Promise<boolean> {
    const previousPurchases = await db.query.subscriptionTransactions.findMany({
      where: and(
        eq(subscriptionTransactions.userId, userId),
        eq(subscriptionTransactions.status, 'completed'),
        eq(subscriptionTransactions.type, 'payment')
      ),
    });

    return previousPurchases.length <= 1; // Current purchase would be included
  }

  // Cancel a subscription
  static async cancelSubscription(subscriptionId: string, reason?: string): Promise<void> {
    await db
      .update(userSubscriptions)
      .set({
        status: 'cancelled',
        cancelledAt: new Date(),
        cancelReason: reason || 'User requested cancellation',
        autoRenew: false,
      })
      .where(eq(userSubscriptions.id, subscriptionId));

    // Create cancellation transaction record
    const subscription = await db.query.userSubscriptions.findFirst({
      where: eq(userSubscriptions.id, subscriptionId),
    });

    if (subscription) {
      await db.insert(subscriptionTransactions).values({
        id: uuidv4(),
        subscriptionId,
        userId: subscription.userId,
        planId: subscription.planId,
        type: 'cancellation',
        amount: '0.00',
        currency: 'USD',
        status: 'completed',
        description: reason || 'Subscription cancelled by user',
        processedAt: new Date(),
      });
    }
  }

  // Upgrade/downgrade subscription
  static async upgradeSubscription(data: UpgradeSubscriptionData): Promise<string> {
    const currentSubscription = await db.query.userSubscriptions.findFirst({
      where: eq(userSubscriptions.id, data.subscriptionId),
    });

    if (!currentSubscription) {
      throw new Error('Subscription not found');
    }

    // Create new subscription with new plan
    const newSubscriptionId = await this.createSubscription({
      userId: currentSubscription.userId,
      planId: data.newPlanId,
      paymentMethod: data.paymentMethod,
    });

    // Create upgrade/downgrade transaction record
    const newPlan = await this.getSubscriptionPlan(data.newPlanId);
    const currentPlan = await this.getSubscriptionPlan(currentSubscription.planId);
    
    if (newPlan && currentPlan) {
      const isUpgrade = parseFloat(newPlan.price) > parseFloat(currentPlan.price);
      
      await db.insert(subscriptionTransactions).values({
        id: uuidv4(),
        subscriptionId: newSubscriptionId,
        userId: currentSubscription.userId,
        planId: data.newPlanId,
        type: isUpgrade ? 'upgrade' : 'downgrade',
        amount: newPlan.price,
        currency: newPlan.currency,
        status: 'pending',
        description: `${isUpgrade ? 'Upgraded' : 'Downgraded'} from ${currentPlan.displayName} to ${newPlan.displayName}`,
      });
    }

    return newSubscriptionId;
  }

  // Get user's billing history
  static async getBillingHistory(userId: string, limit: number = 10): Promise<SubscriptionTransaction[]> {
    const transactions = await db.query.subscriptionTransactions.findMany({
      where: eq(subscriptionTransactions.userId, userId),
      orderBy: [desc(subscriptionTransactions.createdAt)],
      limit,
    });

    return transactions;
  }

  // Create a subscription transaction
  static async createTransaction(data: {
    subscriptionId: string;
    userId: string;
    planId: string;
    type: 'payment' | 'refund' | 'upgrade' | 'downgrade' | 'cancellation';
    amount: string;
    currency?: string;
    paymentGateway?: string;
    gatewayTransactionId?: string;
    description?: string;
    metadata?: any;
  }): Promise<string> {
    const transactionId = uuidv4();

    await db.insert(subscriptionTransactions).values({
      id: transactionId,
      subscriptionId: data.subscriptionId,
      userId: data.userId,
      planId: data.planId,
      type: data.type,
      amount: data.amount,
      currency: data.currency || 'USD',
      status: 'pending',
      paymentGateway: data.paymentGateway || null,
      gatewayTransactionId: data.gatewayTransactionId || null,
      description: data.description || null,
      gatewayResponse: data.metadata || null,
    });

    return transactionId;
  }

  // Update transaction status
  static async updateTransactionStatus(
    transactionId: string,
    status: 'pending' | 'completed' | 'failed' | 'cancelled',
    gatewayResponse?: any
  ): Promise<void> {
    await db
      .update(subscriptionTransactions)
      .set({
        status,
        processedAt: status === 'completed' ? new Date() : null,
        gatewayResponse: gatewayResponse || null,
      })
      .where(eq(subscriptionTransactions.id, transactionId));
  }

  // Check if user can access a feature based on their subscription
  static async canAccessFeature(userId: string, feature: string): Promise<boolean> {
    const status = await this.getUserSubscriptionStatus(userId);
    
    if (!status.isActive || !status.plan) {
      return false;
    }

    const plan = status.plan;

    switch (feature) {
      case 'create_fan_pages':
        return plan.canCreateFanPages;
      case 'create_stores':
        return plan.canCreateStores;
      case 'monetize_blogs':
        return plan.canMonetizeBlogs;
      case 'priority_support':
        return plan.prioritySupport;
      case 'unlimited_posts':
        return plan.maxPosts === -1;
      case 'unlimited_storage':
        return plan.maxStorage === -1;
      case 'unlimited_groups':
        return plan.maxGroups === -1;
      default:
        return false;
    }
  }

  // Get feature limits for user
  static async getFeatureLimits(userId: string) {
    const status = await this.getUserSubscriptionStatus(userId);
    
    if (!status.plan) {
      return {
        maxPosts: 0,
        maxStorage: 0,
        maxGroups: 0,
        canCreateFanPages: false,
        canCreateStores: false,
        canMonetizeBlogs: false,
        prioritySupport: false,
      };
    }

    return {
      maxPosts: status.plan.maxPosts,
      maxStorage: status.plan.maxStorage,
      maxGroups: status.plan.maxGroups,
      canCreateFanPages: status.plan.canCreateFanPages,
      canCreateStores: status.plan.canCreateStores,
      canMonetizeBlogs: status.plan.canMonetizeBlogs,
      prioritySupport: status.plan.prioritySupport,
    };
  }
}
